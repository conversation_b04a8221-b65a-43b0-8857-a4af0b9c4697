import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { QuotationTemplate } from '../../types/quotation_template';
import { Customer } from '../../types/customer';
import { Product } from '../../types/product';
import { QuotationItem } from '../../types/quotation';
import { getAllQuotationTemplates, getDefaultQuotationTemplate, generateQuotationDocument } from '../../services/quotationTemplateService';
import { getAllCustomersNoPage, searchCustomers } from '../../services/customerService';
import { useAuth } from '../../context/AuthContext';
import { useConfirmation } from '../../context/ConfirmationContext';
import { FaSave, FaArrowLeft, FaUserPlus, FaFileAlt } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import ProductSelector from './ProductSelector';
import QuotationItemList from './QuotationItemList';
import NewCustomerModal from '../customers/NewCustomerModal';
import { LABOR_HOUR_RATE } from '../../constants/pricing';
import { MobileContainer, MobilePageHeader, MobileFormGroup, MobileFormActions } from '../common/MobileUtils';
import { useMobile } from '../../hooks/useMobile';

import * as Yup from 'yup';

// Validation schema
const quotationSchema = Yup.object().shape({
  customer_id: Yup.number().required('Klant is verplicht'),
  title: Yup.string().required('Titel is verplicht'),
  valid_until: Yup.date().nullable().typeError('Ongeldige datum'),
  discount_percentage: Yup.number().min(0, 'Korting kan niet negatief zijn').max(100, 'Korting kan niet meer dan 100% zijn').nullable(),
  vat_percentage: Yup.number().min(0, 'BTW kan niet negatief zijn').nullable(),
});

interface QuotationData {
  customer_id?: number;
  title: string;
  introduction: string;
  conclusion: string;
  discount_percentage: number;
  vat_percentage: number;
  valid_until: string;
}

const QuotationTemplateForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();

  const [formData, setFormData] = useState<QuotationData>({
    customer_id: undefined,
    title: '',
    introduction: '',
    conclusion: '',
    discount_percentage: 0,
    vat_percentage: 21,
    valid_until: (() => {
      // Create a date 30 days from now
      const date = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      // Format as YYYY-MM-DDThh:mm for the datetime-local input
      return date.toISOString().slice(0, 16);
    })()
  });

  const [customers, setCustomers] = useState<Customer[]>([]);
  const [items, setItems] = useState<QuotationItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Customer search state
  const [customerSearch, setCustomerSearch] = useState('');
  const [searchResults, setSearchResults] = useState<Customer[]>([]);
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // New customer modal state
  const [showNewCustomerModal, setShowNewCustomerModal] = useState(false);

  // Template state
  const [templates, setTemplates] = useState<QuotationTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<QuotationTemplate | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch customers
        const customersResponse = await getAllCustomersNoPage();
        setCustomers(customersResponse.customers);

        // Fetch quotation templates
        try {
          const templatesResponse = await getAllQuotationTemplates(true);
          setTemplates(templatesResponse.templates);

          // Set default template
          try {
            const defaultTemplate = await getDefaultQuotationTemplate();
            setSelectedTemplate(defaultTemplate);
          } catch (err) {
            // No default template found, select first available template
            if (templatesResponse.templates.length > 0) {
              setSelectedTemplate(templatesResponse.templates[0]);
            }
          }
        } catch (err) {
          console.error('Failed to load templates:', err);
        }
      } catch (err: any) {
        console.error('Failed to fetch data:', err);
        setErrors({
          form: err.response?.data?.error || 'Fout bij ophalen van gegevens'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Customer search functionality
  useEffect(() => {
    if (customerSearch.length >= 2) {
      const delayDebounceFn = setTimeout(() => {
        searchCustomersHandler();
      }, 300);

      return () => clearTimeout(delayDebounceFn);
    } else {
      setSearchResults([]);
      setShowCustomerDropdown(false);
    }
  }, [customerSearch]);

  const searchCustomersHandler = async () => {
    if (customerSearch.length < 2) return;

    try {
      setIsSearching(true);
      const response = await searchCustomers(customerSearch);
      setSearchResults(response.customers);
      setShowCustomerDropdown(true);
    } catch (err: any) {
      console.error('Failed to search customers:', err);
    } finally {
      setIsSearching(false);
    }
  };

  const handleCustomerSelect = (customer: Customer) => {
    setFormData({
      ...formData,
      customer_id: customer.id
    });
    setCustomerSearch(customer.name);
    setShowCustomerDropdown(false);
    
    // Clear customer error
    if (errors.customer_id) {
      setErrors({
        ...errors,
        customer_id: ''
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Parse numeric values
    let parsedValue: any = value;
    if (name === 'discount_percentage' || name === 'vat_percentage') {
      parsedValue = value === '' ? 0 : parseFloat(value);
    }

    setFormData({
      ...formData,
      [name]: parsedValue
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  const validateForm = async (): Promise<boolean> => {
    try {
      await quotationSchema.validate(formData, { abortEarly: false });
      return true;
    } catch (err) {
      if (err instanceof Yup.ValidationError) {
        const validationErrors: Record<string, string> = {};
        err.inner.forEach((error) => {
          if (error.path) {
            validationErrors[error.path] = error.message;
          }
        });
        setErrors(validationErrors);
      }
      return false;
    }
  };

  const handleProductSelected = (product: Product, laborHours?: number) => {
    // Use the selling_price from the product
    const customerPrice = product.selling_price || 0;

    // Create a new item for the product
    const newItem: Partial<QuotationItem> = {
      id: -Date.now(), // Temporary negative ID for new items
      quotation_id: 0,
      product_id: product.id,
      product_name: product.name,
      product_code: product.product_code,
      description: product.name || `Product ${product.id}`,
      quantity: 1,
      unit_price: customerPrice,
      discount_percentage: 0,
      sort_order: items.length,
      total_price: customerPrice
    };

    // Create a new array with the new items
    const newItems = [...items, newItem as QuotationItem];

    // If labor hours are provided, add a labor item
    if (laborHours && laborHours > 0) {
      const laborCost = laborHours * LABOR_HOUR_RATE;

      const laborItem: Partial<QuotationItem> = {
        id: -(Date.now() + 1), // Another temporary negative ID
        quotation_id: 0,
        product_id: null,
        product_name: null,
        product_code: null,
        description: `Arbeid installatie ${product.name}`,
        quantity: laborHours,
        unit_price: LABOR_HOUR_RATE,
        discount_percentage: 0,
        sort_order: items.length + 1,
        total_price: laborCost
      };

      newItems.push(laborItem as QuotationItem);
    }

    setItems(newItems);
  };

  const handleItemUpdate = (updatedItem: QuotationItem) => {
    setItems(items.map(item => 
      item.id === updatedItem.id ? updatedItem : item
    ));
  };

  const handleItemDelete = (itemId: number) => {
    setItems(items.filter(item => item.id !== itemId));
  };

  // Calculate totals
  const subtotal = items.reduce((sum, item) => sum + (item.total_price || 0), 0);
  const discountAmount = subtotal * ((formData.discount_percentage || 0) / 100);
  const totalExclVat = subtotal - discountAmount;
  const vatAmount = totalExclVat * ((formData.vat_percentage || 21) / 100);
  const totalInclVat = totalExclVat + vatAmount;

  const totals = {
    subtotal,
    discountAmount,
    totalExclVat,
    vatAmount,
    totalInclVat
  };

  const handleGenerateDocument = async () => {
    if (!selectedTemplate || !formData.customer_id || items.length === 0) return;

    const isValid = await validateForm();
    if (!isValid) return;

    showConfirmation({
      title: 'Document genereren',
      message: `Wil je een DOCX document genereren van deze offerte met template "${selectedTemplate.name}"?`,
      confirmText: 'Genereren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          setSubmitting(true);

          // Get customer data
          const selectedCustomer = customers.find(c => c.id === formData.customer_id);
          if (!selectedCustomer) {
            throw new Error('Klant niet gevonden');
          }

          // Prepare quotation data for document generation
          const quotationData = {
            ...formData,
            customer_name: selectedCustomer.name,
            customer_address: selectedCustomer.address,
            customer_city: selectedCustomer.city,
            customer_postal_code: selectedCustomer.postal_code,
            customer_phone: selectedCustomer.phone,
            customer_email: selectedCustomer.email,
            created_by: user?.id,
            items: items.map(item => ({
              ...item,
              // Ensure all required fields are present
              description: item.description || item.product_name || '',
              quantity: item.quantity || 1,
              unit_price: item.unit_price || 0,
              discount_percentage: item.discount_percentage || 0,
              total_price: item.total_price || 0
            })),
            subtotal,
            discount_amount: discountAmount,
            total_excl_vat: totalExclVat,
            vat_amount: vatAmount,
            total_incl_vat: totalInclVat
          };

          const result = await generateQuotationDocument(quotationData, selectedTemplate.id);

          // Show success message without navigating immediately
          setErrors({});

          // Show success message and provide navigation option
          showConfirmation({
            title: 'Document succesvol gegenereerd',
            message: `Het document "${result.filename || 'Offerte document'}" is succesvol gegenereerd en opgeslagen bij de klant documenten.`,
            confirmText: 'Ga naar Offertes',
            cancelText: 'Blijf hier',
            onConfirm: () => {
              navigate('/quotations');
            },
            onCancel: () => {
              // Reset form or stay on current page
              setFormData({
                customer_id: '',
                title: '',
                introduction: '',
                conclusion: '',
                valid_until: '',
                discount_percentage: 0
              });
              setItems([]);
            }
          });
        } catch (err: any) {
          console.error('Failed to generate document:', err);

          // Handle authentication errors specifically
          if (err.response?.status === 401) {
            setErrors({
              form: 'Sessie verlopen. Probeer opnieuw in te loggen en het document nogmaals te genereren.'
            });
          } else if (err.response?.status === 403) {
            setErrors({
              form: 'Geen toegang tot deze functie. Neem contact op met een beheerder.'
            });
          } else if (err.response?.status === 500) {
            setErrors({
              form: 'Server fout bij genereren van document. Probeer het later opnieuw.'
            });
          } else {
            setErrors({
              form: err.response?.data?.error || 'Fout bij genereren van document. Probeer het opnieuw.'
            });
          }
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  if (loading) {
    return <LoadingSpinner message="Laden..." />;
  }

  return (
    <MobileContainer>
      <MobilePageHeader
        title="Nieuwe Offerte"
        subtitle="Maak een nieuwe offerte met template"
        showBackButton
        onBack={() => navigate('/quotations')}
      />

      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
        {errors.form && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {errors.form}
          </div>
        )}

        <form className="space-y-6">
          {/* Template Selection */}
          <MobileFormGroup label="Template">
            <select
              value={selectedTemplate?.id || ''}
              onChange={(e) => {
                const templateId = parseInt(e.target.value);
                const template = templates.find(t => t.id === templateId);
                setSelectedTemplate(template || null);
              }}
              className="mobile-form-input"
              disabled={submitting}
            >
              <option value="">Selecteer een template</option>
              {templates.map((template) => (
                <option key={template.id} value={template.id}>
                  {template.name}
                </option>
              ))}
            </select>
          </MobileFormGroup>

          {/* Customer Selection */}
          <MobileFormGroup label="Klant" error={errors.customer_id}>
            <div className="relative">
              <input
                type="text"
                placeholder="Zoek klant..."
                value={customerSearch}
                onChange={(e) => setCustomerSearch(e.target.value)}
                className={`mobile-form-input ${errors.customer_id ? 'border-red-500' : ''}`}
                disabled={submitting}
              />
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <LoadingSpinner size="sm" />
                </div>
              )}

              {showCustomerDropdown && searchResults.length > 0 && (
                <div className="absolute z-10 w-full bg-white dark:bg-dark-card border border-gray-300 dark:border-gray-600 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">
                  {searchResults.map((customer) => (
                    <div
                      key={customer.id}
                      className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-secondary cursor-pointer"
                      onClick={() => handleCustomerSelect(customer)}
                    >
                      <div className="font-medium">{customer.name}</div>
                      <div className="text-sm text-gray-500 dark:text-dark-text-light">
                        {customer.email} - {customer.city}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <button
              type="button"
              onClick={() => setShowNewCustomerModal(true)}
              className="mt-2 btn btn-secondary btn-sm"
              disabled={submitting}
            >
              <FaUserPlus className="mr-2" /> Nieuwe klant
            </button>
          </MobileFormGroup>

          {/* Basic Quotation Fields */}
          <MobileFormGroup label="Titel" error={errors.title}>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className={`mobile-form-input ${errors.title ? 'border-red-500' : ''}`}
              placeholder="Offerte titel"
              disabled={submitting}
            />
          </MobileFormGroup>

          <MobileFormGroup label="Inleiding">
            <textarea
              name="introduction"
              value={formData.introduction}
              onChange={handleInputChange}
              className="mobile-form-input"
              placeholder="Optionele inleiding voor de offerte"
              rows={3}
              disabled={submitting}
            />
          </MobileFormGroup>

          <MobileFormGroup label="Conclusie">
            <textarea
              name="conclusion"
              value={formData.conclusion}
              onChange={handleInputChange}
              className="mobile-form-input"
              placeholder="Optionele conclusie voor de offerte"
              rows={3}
              disabled={submitting}
            />
          </MobileFormGroup>

          <div className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-3 gap-4'}`}>
            <MobileFormGroup label="Korting %" error={errors.discount_percentage}>
              <input
                type="number"
                name="discount_percentage"
                value={formData.discount_percentage}
                onChange={handleInputChange}
                className={`mobile-form-input ${errors.discount_percentage ? 'border-red-500' : ''}`}
                min="0"
                max="100"
                step="0.01"
                disabled={submitting}
              />
            </MobileFormGroup>

            <MobileFormGroup label="BTW %" error={errors.vat_percentage}>
              <input
                type="number"
                name="vat_percentage"
                value={formData.vat_percentage}
                onChange={handleInputChange}
                className={`mobile-form-input ${errors.vat_percentage ? 'border-red-500' : ''}`}
                min="0"
                step="0.01"
                disabled={submitting}
              />
            </MobileFormGroup>

            <MobileFormGroup label="Geldig tot" error={errors.valid_until}>
              <input
                type="datetime-local"
                name="valid_until"
                value={formData.valid_until}
                onChange={handleInputChange}
                className={`mobile-form-input ${errors.valid_until ? 'border-red-500' : ''}`}
                disabled={submitting}
              />
            </MobileFormGroup>
          </div>
        </form>
      </div>

      {/* Product Selection */}
      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
        <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
          Producten toevoegen
        </h3>
        <ProductSelector onProductSelected={handleProductSelected} />
      </div>

      {/* Items List */}
      {items.length > 0 && (
        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
            Offerte items
          </h3>
          <QuotationItemList
            items={items}
            onItemUpdate={handleItemUpdate}
            onItemDelete={handleItemDelete}
            totals={totals}
            isEditing={true}
          />
        </div>
      )}

      {/* Generate Document Button */}
      {items.length > 0 && selectedTemplate && formData.customer_id && (
        <MobileFormActions>
          <button
            type="button"
            onClick={handleGenerateDocument}
            className="btn btn-primary"
            disabled={submitting || !selectedTemplate}
          >
            {submitting ? (
              <>
                <LoadingSpinner size="sm" /> Genereren...
              </>
            ) : (
              <>
                <FaFileAlt className="mr-2" /> Document genereren
              </>
            )}
          </button>
        </MobileFormActions>
      )}

      {/* New Customer Modal */}
      {showNewCustomerModal && (
        <NewCustomerModal
          onClose={() => setShowNewCustomerModal(false)}
          onCustomerCreated={(customer) => {
            setCustomers([...customers, customer]);
            handleCustomerSelect(customer);
            setShowNewCustomerModal(false);
          }}
        />
      )}
    </MobileContainer>
  );
};

export default QuotationTemplateForm;
