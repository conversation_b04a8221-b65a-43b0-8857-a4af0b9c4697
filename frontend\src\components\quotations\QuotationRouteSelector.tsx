import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FaFileInvoiceDollar, FaRocket, FaFileAlt } from 'react-icons/fa';

const QuotationRouteSelector: React.FC = () => {
  const navigate = useNavigate();

  const routes = [
    {
      id: 'template',
      title: 'Template Offerte',
      description: 'Gebruik een template om een offerte te maken',
      icon: <FaFileAlt className="text-4xl text-blue-500" />,
      path: '/quotations/new-template',
      color: 'border-blue-500 hover:bg-blue-50'
    },
    {
      id: 'quicksale',
      title: 'Snelle Verkoop',
      description: 'Voor particulieren - Alarm, Camera\'s of beide met vaste prijzen',
      icon: <FaRocket className="text-4xl text-green-500" />,
      path: '/quotations/new-quicksale',
      color: 'border-green-500 hover:bg-green-50'
    },
    {
      id: 'standard',
      title: 'Standaard Offerte',
      description: 'Maak een offerte vanaf nul',
      icon: <FaFileInvoiceDollar className="text-4xl text-gray-500" />,
      path: '/quotations/new-standard',
      color: 'border-gray-500 hover:bg-gray-50'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Nieuwe Offerte</h1>
        <p className="text-gray-600">Kies hoe je een nieuwe offerte wilt maken</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {routes.map((route) => (
          <div
            key={route.id}
            onClick={() => navigate(route.path)}
            className={`
              border-2 rounded-lg p-6 cursor-pointer transition-all duration-200
              ${route.color}
              hover:shadow-lg transform hover:-translate-y-1
            `}
          >
            <div className="text-center">
              <div className="mb-4 flex justify-center">
                {route.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {route.title}
              </h3>
              <p className="text-gray-600 text-sm">
                {route.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 text-center">
        <button
          onClick={() => navigate('/quotations')}
          className="text-gray-500 hover:text-gray-700 underline"
        >
          Terug naar offertes
        </button>
      </div>
    </div>
  );
};

export default QuotationRouteSelector;
