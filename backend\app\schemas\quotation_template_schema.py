"""
Quotation Template schema module.
This module defines the schema for QuotationTemplate model validation.
"""
from marshmallow import fields, validate
from app.schemas import ma
from app.models.quotation_template import QuotationTemplate

class QuotationTemplateSchema(ma.SQLAlchemySchema):
    """Schema for QuotationTemplate model."""

    class Meta:
        """Meta class for QuotationTemplateSchema."""
        model = QuotationTemplate
        load_instance = True

    id = ma.auto_field(dump_only=True)
    name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    description = fields.String(allow_none=True)
    file_path = fields.String(dump_only=True)
    file_type = fields.String(dump_only=True)
    is_active = fields.Boolean(missing=True)
    is_default = fields.Boolean(missing=False)
    created_by = fields.Integer(required=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Additional fields for response
    created_by_name = fields.String(dump_only=True)

# Create instances of the schema for single and multiple quotation templates
quotation_template_schema = QuotationTemplateSchema()
quotation_templates_schema = QuotationTemplateSchema(many=True)
