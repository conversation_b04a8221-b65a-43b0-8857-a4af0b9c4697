"""
Quotation controller module.
This module provides API endpoints for quotations.
"""
from flask import Blueprint, request, jsonify, send_file
from app.services.quotation_service import QuotationService
from app.utils.security import token_required, role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.schemas.quotation_schema import quotation_schema, quotation_item_schema
import logging
from marshmallow import ValidationError

# Define the blueprint for quotation-related routes
quotation_bp = Blueprint("quotation", __name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the quotation service
quotation_service = QuotationService()

@quotation_bp.route("", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_all_quotations():
    """
    Get all quotations with pagination.

    Returns:
        JSON: List of quotations and pagination info.
    """
    try:
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))

        quotations, total = quotation_service.get_all_quotations(page, per_page)

        return jsonify({
            "quotations": quotations,
            "total": total,
            "page": page,
            "per_page": per_page
        }), 200
    except Exception as e:
        logger.error(f"Failed to fetch quotations: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("/<int:quotation_id>", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_quotation(quotation_id):
    """
    Get a quotation by ID.

    Args:
        quotation_id: Quotation ID

    Returns:
        JSON: Quotation data.
    """
    try:
        quotation = quotation_service.get_quotation_by_id(quotation_id)
        return jsonify(quotation), 200
    except Exception as e:
        logger.error(f"Failed to fetch quotation {quotation_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@quotation_bp.route("/customer/<int:customer_id>", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_quotations_by_customer(customer_id):
    """
    Get quotations for a customer with pagination.

    Args:
        customer_id: Customer ID

    Returns:
        JSON: List of quotations and pagination info.
    """
    try:
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))

        quotations, total = quotation_service.get_quotations_by_customer(customer_id, page, per_page)

        return jsonify({
            "quotations": quotations,
            "total": total,
            "page": page,
            "per_page": per_page
        }), 200
    except Exception as e:
        logger.error(f"Failed to fetch quotations for customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("/search", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def search_quotations():
    """
    Search quotations by title, quotation number, or customer name.

    Returns:
        JSON: List of matching quotations and pagination info.
    """
    try:
        search_term = request.args.get("q", "")
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))

        quotations, total = quotation_service.search_quotations(search_term, page, per_page)

        return jsonify({
            "quotations": quotations,
            "total": total,
            "page": page,
            "per_page": per_page
        }), 200
    except Exception as e:
        logger.error(f"Failed to search quotations: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("", methods=["POST"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def create_quotation():
    """
    Create a new quotation.

    Returns:
        JSON: Created quotation data.
    """
    try:
        quotation_data = request.json

        # Set created_by to current user
        quotation_data["created_by"] = request.current_user.id

        # Validate with schema
        errors = quotation_schema.validate(quotation_data)
        if errors:
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Create quotation
        quotation = quotation_service.create_quotation(quotation_data)

        return jsonify(quotation), 201
    except ValidationError as e:
        logger.warning(f"Quotation validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create quotation: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("/<int:quotation_id>", methods=["PUT"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def update_quotation(quotation_id):
    """
    Update a quotation.

    Args:
        quotation_id: Quotation ID

    Returns:
        JSON: Updated quotation data.
    """
    try:
        quotation_data = request.json

        # Log the incoming data for debugging
        logger.info(f"Updating quotation {quotation_id} with data: {quotation_data}")

        # We'll skip validation here since the service layer will handle it
        # and will fill in missing required fields from the existing quotation

        # Update quotation
        quotation = quotation_service.update_quotation(quotation_id, quotation_data)

        return jsonify(quotation), 200
    except ValidationError as e:
        logger.warning(f"Quotation validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update quotation {quotation_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("/<int:quotation_id>", methods=["DELETE"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def delete_quotation(quotation_id):
    """
    Delete a quotation.

    Args:
        quotation_id: Quotation ID

    Returns:
        JSON: Success message.
    """
    try:
        success = quotation_service.delete_quotation(quotation_id)

        if success:
            return jsonify({"message": "Quotation deleted successfully"}), 200
        else:
            return jsonify({"error": "Quotation not found"}), 404
    except Exception as e:
        logger.error(f"Failed to delete quotation {quotation_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("/<int:quotation_id>/items", methods=["POST"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def add_item_to_quotation(quotation_id):
    """
    Add an item to a quotation.

    Args:
        quotation_id: Quotation ID

    Returns:
        JSON: Added item data.
    """
    try:
        item_data = request.json

        # Log the received data for debugging
        logger.info(f"Received item data: {item_data}")

        # Add quotation_id to item_data for validation
        validation_data = item_data.copy()
        validation_data['quotation_id'] = quotation_id

        # Ensure description is present
        if 'product_id' in item_data and not item_data.get('description'):
            # Try to get product details to set description
            try:
                from app.repositories.product_repository import ProductRepository
                product_repo = ProductRepository()
                product = product_repo.get_by_id(item_data['product_id'])
                if product:
                    validation_data['description'] = product.name
            except Exception as e:
                logger.error(f"Error getting product details: {str(e)}")

        # Validate with schema
        errors = quotation_item_schema.validate(validation_data)
        if errors:
            logger.warning(f"Validation errors: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        try:
            # Add item to quotation
            item = quotation_service.add_item_to_quotation(quotation_id, validation_data)
            return jsonify(item), 201
        except Exception as e:
            logger.error(f"Error adding item to quotation: {str(e)}")
            return jsonify({"error": str(e)}), 500
    except ValidationError as e:
        logger.warning(f"Quotation item validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to add item to quotation {quotation_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("/items/<int:item_id>", methods=["PUT"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def update_item(item_id):
    """
    Update a quotation item.

    Args:
        item_id: Item ID

    Returns:
        JSON: Updated item data.
    """
    try:
        item_data = request.json

        # We'll skip validation for update since we don't have easy access to the quotation_id
        # The service layer will handle validation and return appropriate errors

        # Update item
        item = quotation_service.update_item(item_id, item_data)

        return jsonify(item), 200
    except ValidationError as e:
        logger.warning(f"Quotation item validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update item {item_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_bp.route("/items/<int:item_id>", methods=["DELETE"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def delete_item(item_id):
    """
    Delete a quotation item.

    Args:
        item_id: Item ID

    Returns:
        JSON: Success message.
    """
    try:
        success = quotation_service.delete_item(item_id)

        if success:
            return jsonify({"message": "Item deleted successfully"}), 200
        else:
            return jsonify({"error": "Item not found"}), 404
    except Exception as e:
        logger.error(f"Failed to delete item {item_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500



@quotation_bp.route("/<int:quotation_id>/status", methods=["PUT"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def update_quotation_status(quotation_id):
    """
    Update only the status of a quotation.

    Args:
        quotation_id: Quotation ID

    Returns:
        JSON: Updated quotation data.
    """
    try:
        status_data = request.json

        if not status_data or "status" not in status_data:
            return jsonify({"error": "Status is required"}), 400

        # Update quotation status
        quotation = quotation_service.update_quotation_status(quotation_id, status_data["status"])

        return jsonify(quotation), 200
    except Exception as e:
        logger.error(f"Failed to update quotation status {quotation_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500



@quotation_bp.route("/<int:quotation_id>/process-rejected", methods=["POST"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def process_single_rejected_quotation(quotation_id):
    """
    Process a single rejected quotation by deleting it and its associated customer
    if the customer has no other quotations.

    Args:
        quotation_id: Quotation ID

    Returns:
        JSON: Information about the operation.
    """
    try:
        result = quotation_service.process_single_rejected_quotation(quotation_id)

        return jsonify({
            "message": "Quotation rejected and processed successfully",
            "quotation_deleted": result["quotation_deleted"],
            "customer_deleted": result["customer_deleted"]
        }), 200
    except Exception as e:
        logger.error(f"Failed to process rejected quotation {quotation_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500
