#!/usr/bin/env python3
"""
Test script for quotation template functionality.
This script tests the Firebase Storage integration for quotation templates.
"""

import os
import sys
import tempfile
import io
import requests
import json

# Simple test without Flask app context to test API endpoints

def create_test_docx_file():
    """Create a simple test DOCX file."""
    # Create a minimal DOCX file content (this is a simplified version)
    # In a real scenario, you'd use python-docx to create a proper DOCX file
    docx_content = b'PK\x03\x04\x14\x00\x00\x00\x08\x00'  # DOCX file header
    
    # Create a file-like object
    file_obj = io.BytesIO(docx_content)
    file_obj.filename = 'test_quotation_template.docx'
    file_obj.seek(0)
    
    return FileStorage(
        stream=file_obj,
        filename='test_quotation_template.docx',
        content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    )

def test_quotation_template_creation():
    """Test creating a quotation template."""
    print("Testing quotation template creation...")
    
    app = create_app()
    
    with app.app_context():
        # Create a test user if it doesn't exist
        test_user = User.query.filter_by(email='<EMAIL>').first()
        if not test_user:
            test_user = User(
                username='testuser',
                email='<EMAIL>',
                role='administrator'
            )
            test_user.set_password('testpassword')
            db.session.add(test_user)
            db.session.commit()
        
        # Create test file
        test_file = create_test_docx_file()
        
        # Test data
        template_data = {
            'name': 'Test Quotation Template',
            'description': 'A test template for quotations',
            'is_active': True,
            'is_default': True,
            'created_by': test_user.id
        }
        
        # Test the service
        service = QuotationTemplateService()
        
        try:
            # Create template
            template = service.create_template(template_data, test_file)
            print(f"✓ Successfully created template: {template['name']} (ID: {template['id']})")
            
            # Test getting the template
            retrieved_template = service.get_template_by_id(template['id'])
            if retrieved_template:
                print(f"✓ Successfully retrieved template: {retrieved_template['name']}")
            else:
                print("✗ Failed to retrieve template")
                return False
            
            # Test getting default template
            default_template = service.get_default_template()
            if default_template and default_template['id'] == template['id']:
                print(f"✓ Successfully retrieved default template: {default_template['name']}")
            else:
                print("✗ Failed to retrieve default template")
                return False
            
            # Test downloading template content
            content = service.download_template_content(template['id'])
            if content:
                print(f"✓ Successfully downloaded template content ({len(content)} bytes)")
            else:
                print("✗ Failed to download template content")
                return False
            
            # Clean up - delete the test template
            repo = QuotationTemplateRepository()
            if repo.delete(template['id']):
                print("✓ Successfully cleaned up test template")
            else:
                print("✗ Failed to clean up test template")
            
            return True
            
        except Exception as e:
            print(f"✗ Error during template creation: {str(e)}")
            return False

def test_quotation_template_list():
    """Test listing quotation templates."""
    print("\nTesting quotation template listing...")
    
    app = create_app()
    
    with app.app_context():
        service = QuotationTemplateService()
        
        try:
            # Get all templates
            templates = service.get_all_templates()
            print(f"✓ Successfully retrieved {len(templates)} templates")
            
            # Get only active templates
            active_templates = service.get_all_templates(active_only=True)
            print(f"✓ Successfully retrieved {len(active_templates)} active templates")
            
            return True
            
        except Exception as e:
            print(f"✗ Error during template listing: {str(e)}")
            return False

def test_firebase_storage_integration():
    """Test Firebase Storage integration specifically."""
    print("\nTesting Firebase Storage integration...")
    
    try:
        from app.utils.firebase import upload_file_to_storage, download_file_from_storage, delete_file_from_storage
        
        # Create a test file
        test_content = b"Test quotation template content"
        test_file = io.BytesIO(test_content)
        test_file.filename = 'test_firebase_template.docx'
        test_file.seek(0)
        
        # Test upload
        file_url, storage_path = upload_file_to_storage(test_file, 'quotation_templates/test_template.docx')
        print(f"✓ Successfully uploaded to Firebase Storage: {storage_path}")
        
        # Test download
        downloaded_content, content_type = download_file_from_storage(storage_path)
        if downloaded_content == test_content:
            print("✓ Successfully downloaded and verified content from Firebase Storage")
        else:
            print("✗ Downloaded content doesn't match uploaded content")
            return False
        
        # Test delete
        if delete_file_from_storage(storage_path):
            print("✓ Successfully deleted file from Firebase Storage")
        else:
            print("✗ Failed to delete file from Firebase Storage")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error during Firebase Storage test: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("Starting quotation template tests...\n")
    
    tests = [
        test_firebase_storage_integration,
        test_quotation_template_list,
        test_quotation_template_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"✗ Test failed with exception: {str(e)}\n")
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Quotation templates are working correctly with Firebase Storage.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
