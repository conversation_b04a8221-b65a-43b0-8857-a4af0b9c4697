import api from "../api";
import { QuotationTemplate, QuotationTemplatesResponse, CreateQuotationTemplateData, UpdateQuotationTemplateData } from "../types/quotation_template";

export const getAllQuotationTemplates = async (activeOnly: boolean = true): Promise<QuotationTemplatesResponse> => {
  const response = await api.get(`/quotation-templates?active_only=${activeOnly}`);
  return response.data;
};

export const getQuotationTemplateById = async (templateId: number): Promise<QuotationTemplate> => {
  const response = await api.get(`/quotation-templates/${templateId}`);
  return response.data;
};

export const getDefaultQuotationTemplate = async (): Promise<QuotationTemplate> => {
  const response = await api.get(`/quotation-templates/default`);
  return response.data;
};

export const createQuotationTemplate = async (
  templateData: CreateQuotationTemplateData,
  file: File
): Promise<QuotationTemplate> => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("name", templateData.name);
  
  if (templateData.description) {
    formData.append("description", templateData.description);
  }
  
  if (templateData.is_active !== undefined) {
    formData.append("is_active", templateData.is_active.toString());
  }
  
  if (templateData.is_default !== undefined) {
    formData.append("is_default", templateData.is_default.toString());
  }

  const response = await api.post("/quotation-templates", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const updateQuotationTemplate = async (
  templateId: number,
  templateData: UpdateQuotationTemplateData,
  file?: File
): Promise<QuotationTemplate> => {
  const formData = new FormData();
  
  if (file) {
    formData.append("file", file);
  }
  
  if (templateData.name) {
    formData.append("name", templateData.name);
  }
  
  if (templateData.description !== undefined) {
    formData.append("description", templateData.description || "");
  }
  
  if (templateData.is_active !== undefined) {
    formData.append("is_active", templateData.is_active.toString());
  }
  
  if (templateData.is_default !== undefined) {
    formData.append("is_default", templateData.is_default.toString());
  }

  const response = await api.put(`/quotation-templates/${templateId}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const deleteQuotationTemplate = async (templateId: number): Promise<void> => {
  await api.delete(`/quotation-templates/${templateId}`);
};

export const setDefaultQuotationTemplate = async (templateId: number): Promise<void> => {
  await api.post(`/quotation-templates/${templateId}/set-default`);
};

export const downloadQuotationTemplate = async (templateId: number): Promise<Blob> => {
  const response = await api.get(`/quotation-templates/${templateId}/download`, {
    responseType: "blob",
  });
  return response.data;
};

export const getQuotationTemplateFileUrl = (templateId: number): string => {
  return `${api.defaults.baseURL}/quotation-templates/${templateId}/download`;
};

export const generateQuotationDocument = async (
  quotationData: any,
  templateId?: number
): Promise<any> => {
  const response = await api.post("/quotation-templates/generate", {
    quotation_data: quotationData,
    template_id: templateId
  }, {
    timeout: 30000 // 30 seconds timeout for document generation
  });
  return response.data;
};
