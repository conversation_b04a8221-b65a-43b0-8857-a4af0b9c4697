SIMPLE QUOTATION TEMPLATE (Copy to Word)
========================================

                                AMSPM
                        {{bedrijf_adres}}
                    Tel: {{bedrijf_telefoon}}
                    Email: {{bedrijf_email}}

================================================================================

                                OFFERTE

Offerte nummer: {{offerte_nummer}}
Titel: {{titel}}
Datum: {{datum}}
Geldig tot: {{geldig_tot}}

--------------------------------------------------------------------------------

KLANTGEGEVENS:
Naam: {{klant_naam}}
Adres: {{klant_adres}}
Telefoon: {{klant_telefoon}}
Email: {{klant_email}}

--------------------------------------------------------------------------------

INTRODUCTIE:
{{introductie}}

--------------------------------------------------------------------------------

PRODUCTEN EN DIENSTEN:

{%- for product in producten %}
Product: {{product.omschrijving}}
Code: {{product.product_code}}
Aantal: {{product.aantal}}
Eenheidsprijs: {{product.eenheidsprijs}}
Totaalprijs: {{product.totaalprijs}}

{%- endfor %}

--------------------------------------------------------------------------------

FINANCIEEL OVERZICHT:

Subtotaal (excl. BTW):          {{subtotaal}}
Korting ({{korting_percentage}}%):                {{korting_bedrag}}
Totaal excl. BTW:              {{totaal_excl_btw}}
BTW ({{btw_percentage}}%):                        {{btw_bedrag}}
Totaal incl. BTW:              {{totaal_incl_btw}}

--------------------------------------------------------------------------------

CONCLUSIE:
{{conclusie}}

--------------------------------------------------------------------------------

Met vriendelijke groet,

AMSPM

================================================================================

INSTRUCTIONS:
1. Copy this entire content
2. Paste into a new Word document
3. Format it nicely (fonts, spacing, etc.)
4. Save as .docx file
5. Upload to your quotation templates

This template uses simple Jinja2 syntax that should work with docxtpl.
The {%- and -%} syntax removes extra whitespace around loops.
