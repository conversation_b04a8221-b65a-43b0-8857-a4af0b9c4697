export interface QuotationTemplate {
  id: number;
  name: string;
  description: string | null;
  file_path: string;
  file_type: string;
  is_active: boolean;
  is_default: boolean;
  created_by: number;
  created_by_name: string | null;
  created_at: string;
  updated_at: string;
}

export interface QuotationTemplatesResponse {
  templates: QuotationTemplate[];
}

export interface CreateQuotationTemplateData {
  name: string;
  description?: string;
  is_active?: boolean;
  is_default?: boolean;
}

export interface UpdateQuotationTemplateData {
  name?: string;
  description?: string;
  is_active?: boolean;
  is_default?: boolean;
}
