AMSPM QUOTATION TEMPLATE STRUCTURE
==================================

This is the structure for your Word template. Copy this into Word and replace the placeholders with the actual Jinja2 syntax.

HEADER SECTION:
---------------
Company Logo: [Insert your logo here]
Company Name: {{bedrijf_naam}}
Address: {{bedrijf_adres}}
Phone: {{bedrijf_telefoon}}
Email: {{bedrijf_email}}

QUOTATION DETAILS:
------------------
OFFERTE

Offerte nummer: {{offerte_nummer}}
Titel: {{titel}}
Datum: {{datum}}
Geldig tot: {{geldig_tot}}

CUSTOMER INFORMATION:
--------------------
Klant: {{klant_naam}}
Adres: {{klant_adres}}
Telefoon: {{klant_telefoon}}
Email: {{klant_email}}

INTRODUCTION:
-------------
{{introductie}}

PRODUCTS TABLE:
--------------
| Product Code | Omschrijving | Aantal | Eenheidsprijs | Korting % | Totaalprijs |
|--------------|--------------|--------|---------------|-----------|-------------|
{% for product in producten %}
| {{product.product_code}} | {{product.omschrijving}} | {{product.aantal}} | {{product.eenheidsprijs}} | {{product.korting_percentage}}% | {{product.totaalprijs}} |
{% endfor %}

FINANCIAL SUMMARY:
-----------------
Subtotaal (excl. BTW): {{subtotaal}}
Korting ({{korting_percentage}}%): {{korting_bedrag}}
Totaal excl. BTW: {{totaal_excl_btw}}
BTW ({{btw_percentage}}%): {{btw_bedrag}}
Totaal incl. BTW: {{totaal_incl_btw}}

CONCLUSION:
-----------
{{conclusie}}

INSTRUCTIONS FOR CREATING THE WORD TEMPLATE:
============================================

1. Open Microsoft Word
2. Create a new document
3. Copy the structure above and format it nicely
4. Replace the table structure with an actual Word table
5. For the products loop, you need to:
   - Create a table with the headers
   - In the first data row, put the Jinja2 template tags
   - Select the entire data row
   - Insert a "Table Row" content control
   - Set the content control to repeat for each item in "producten"

IMPORTANT TEMPLATE SYNTAX RULES:
===============================

1. Use double curly braces for variables: {{variable_name}}
2. Use {% %} for loops and logic
3. For the products table, use Word's built-in table repeating functionality
4. Make sure all braces are properly closed
5. Don't use Word comments near template tags
6. Test the template with simple data first

ALTERNATIVE SIMPLE APPROACH:
============================

If the table loop is causing issues, you can create a simpler template without the loop:
- Just use static placeholders for individual products
- Or create multiple product sections manually

Available Variables:
- offerte_nummer, titel, datum, geldig_tot
- introductie, conclusie
- klant_naam, klant_adres, klant_telefoon, klant_email
- bedrijf_naam, bedrijf_adres, bedrijf_telefoon, bedrijf_email
- subtotaal, korting_percentage, korting_bedrag
- totaal_excl_btw, btw_percentage, btw_bedrag, totaal_incl_btw
- producten (array with: product_code, product_naam, omschrijving, aantal, eenheidsprijs, korting_percentage, totaalprijs)
