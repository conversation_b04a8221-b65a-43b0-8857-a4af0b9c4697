import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaShieldAlt, FaVideo, FaCombine, FaArrowLeft, FaPlus, FaMinus, FaFileAlt } from 'react-icons/fa';
import { Customer } from '../../types/customer';
import { QuotationTemplate } from '../../types/quotation_template';
import { getAllCustomersNoPage } from '../../services/customerService';
import { getAllQuotationTemplates, getDefaultQuotationTemplate, generateQuotationDocument } from '../../services/quotationTemplateService';
import { useAuth } from '../../context/AuthContext';
import { useConfirmation } from '../../context/ConfirmationContext';
import LoadingSpinner from '../LoadingSpinner';

interface QuickSaleCategory {
  id: 'alarm' | 'cameras' | 'alarm_cameras';
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

interface BaseSystemItem {
  name: string;
  quantity: number;
  priceExVat: number;
  required: boolean;
}

interface QuickSaleConfig {
  customerId: string;
  baseDiscount: number; // 0-25%
  videoDoorbell: {
    included: boolean;
    free: boolean;
  };
  extraItems: BaseSystemItem[];
}

const QuickSaleForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const [currentStep, setCurrentStep] = useState<'category' | 'configuration'>('category');
  const [selectedCategory, setSelectedCategory] = useState<QuickSaleCategory | null>(null);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [templates, setTemplates] = useState<QuotationTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<QuotationTemplate | null>(null);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [config, setConfig] = useState<QuickSaleConfig>({
    customerId: '',
    baseDiscount: 0,
    videoDoorbell: { included: false, free: false },
    extraItems: []
  });

  // Base system items (always included)
  const baseSystemItems: BaseSystemItem[] = [
    { name: 'CENTRALE HUB', quantity: 1, priceExVat: 106.26, required: true },
    { name: 'BEDIENDEEL', quantity: 1, priceExVat: 46.00, required: true },
    { name: 'SIRENE', quantity: 1, priceExVat: 26.68, required: true },
    { name: 'PIRCAM', quantity: 2, priceExVat: 73.60, required: true },
    { name: 'SHOCK SENSOR', quantity: 2, priceExVat: 22.08, required: true },
    { name: 'MAGNEETCONTACT', quantity: 1, priceExVat: 16.10, required: true },
    { name: 'BRANDMELDER', quantity: 2, priceExVat: 34.04, required: true }
  ];

  // Available extra items
  const availableExtraItems: BaseSystemItem[] = [
    { name: 'PIRCAM', quantity: 1, priceExVat: 73.60, required: false },
    { name: 'SHOCK SENSOR', quantity: 1, priceExVat: 22.08, required: false },
    { name: 'MAGNEETCONTACT', quantity: 1, priceExVat: 16.10, required: false },
    { name: 'BRANDMELDER', quantity: 1, priceExVat: 34.04, required: false }
  ];

  const videoDoorbell = { name: 'VIDEODEURBEL', priceExVat: 206.61, priceInclVat: 249.99 };

  // Pricing calculations
  const calculatePricing = () => {
    const baseSystemTotalExVat = baseSystemItems.reduce((total, item) =>
      total + (item.priceExVat * item.quantity), 0
    );

    const extraItemsTotalExVat = config.extraItems.reduce((total, item) =>
      total + (item.priceExVat * item.quantity), 0
    );

    const totalItemsExVat = baseSystemTotalExVat + extraItemsTotalExVat;
    const laborCost = 100; // Base labor cost
    const extraLaborCost = config.extraItems.reduce((total, item) => total + (item.quantity * 10), 0);

    // Installation costs with margin
    const totalCostExVat = totalItemsExVat + laborCost + extraLaborCost;
    const marginMultiplier = 1.1155; // 11.55% margin
    const installationCostWithMargin = totalCostExVat * marginMultiplier;

    // Apply base discount
    const discountMultiplier = 1 - (config.baseDiscount / 100);
    const finalInstallationCost = installationCostWithMargin * discountMultiplier;

    // Monthly costs calculation
    const baseMonthlyCost = 32.99; // Base monthly cost for items
    const extraMonthlyPercentage = extraItemsTotalExVat / baseSystemTotalExVat;
    const extraMonthlyCost = baseMonthlyCost * extraMonthlyPercentage;
    const totalMonthlyCost = 49.99 + extraMonthlyCost; // 49.99 is minimum

    // Videodeurbel cost
    const videoDoorbelCost = config.videoDoorbell.included
      ? (config.videoDoorbell.free ? 0 : videoDoorbell.priceInclVat)
      : 0;

    return {
      baseSystemTotalExVat,
      extraItemsTotalExVat,
      totalItemsExVat,
      installationCost: finalInstallationCost + videoDoorbelCost,
      monthlyCost: totalMonthlyCost,
      videoDoorbelCost
    };
  };

  const pricing = calculatePricing();

  const categories: QuickSaleCategory[] = [
    {
      id: 'alarm',
      title: 'ALARM',
      description: 'Alarmsysteem voor beveiliging van uw woning',
      icon: <FaShieldAlt className="text-6xl text-red-500" />,
      color: 'border-red-500 hover:bg-red-50'
    },
    {
      id: 'cameras',
      title: 'CAMERAS',
      description: 'Camerasysteem voor bewaking en opname',
      icon: <FaVideo className="text-6xl text-blue-500" />,
      color: 'border-blue-500 hover:bg-blue-50'
    },
    {
      id: 'alarm_cameras',
      title: 'ALARM + CAMERAS',
      description: 'Compleet systeem met alarm en cameras',
      icon: <FaCombine className="text-6xl text-green-500" />,
      color: 'border-green-500 hover:bg-green-50'
    }
  ];

  useEffect(() => {
    fetchCustomers();
    fetchTemplates();
  }, []);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await getAllCustomersNoPage();
      setCustomers(response);
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      const response = await getAllQuotationTemplates();
      setTemplates(response.templates);

      // Try to get default template
      try {
        const defaultTemplate = await getDefaultQuotationTemplate();
        setSelectedTemplate(defaultTemplate);
      } catch (error) {
        // If no default template, use first available
        if (response.templates.length > 0) {
          setSelectedTemplate(response.templates[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const handleCategorySelect = (category: QuickSaleCategory) => {
    setSelectedCategory(category);
    setCurrentStep('configuration');
  };

  const handleBack = () => {
    if (currentStep === 'configuration') {
      setCurrentStep('category');
      setSelectedCategory(null);
    } else {
      navigate('/quotations');
    }
  };

  const handleAddExtraItem = (item: BaseSystemItem) => {
    const existingIndex = config.extraItems.findIndex(extra => extra.name === item.name);
    if (existingIndex >= 0) {
      const updatedItems = [...config.extraItems];
      updatedItems[existingIndex].quantity += 1;
      setConfig({ ...config, extraItems: updatedItems });
    } else {
      setConfig({
        ...config,
        extraItems: [...config.extraItems, { ...item, quantity: 1 }]
      });
    }
  };

  const handleRemoveExtraItem = (itemName: string) => {
    const existingIndex = config.extraItems.findIndex(extra => extra.name === itemName);
    if (existingIndex >= 0) {
      const updatedItems = [...config.extraItems];
      if (updatedItems[existingIndex].quantity > 1) {
        updatedItems[existingIndex].quantity -= 1;
      } else {
        updatedItems.splice(existingIndex, 1);
      }
      setConfig({ ...config, extraItems: updatedItems });
    }
  };

  const getExtraItemQuantity = (itemName: string): number => {
    const item = config.extraItems.find(extra => extra.name === itemName);
    return item ? item.quantity : 0;
  };

  const handleGenerateDocument = async () => {
    if (!selectedTemplate || !config.customerId) return;

    const selectedCustomer = customers.find(c => c.id === config.customerId);
    if (!selectedCustomer) {
      alert('Klant niet gevonden');
      return;
    }

    showConfirmation({
      title: 'Snelle Verkoop Document Genereren',
      message: `Wil je een DOCX document genereren voor ${selectedCustomer.name} met template "${selectedTemplate.name}"?`,
      confirmText: 'Genereren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          setGenerating(true);

          // Prepare quotation data for template
          const allItems = [
            ...baseSystemItems.map(item => ({
              name: item.name,
              quantity: item.quantity,
              unit_price: item.priceExVat,
              total_price: item.priceExVat * item.quantity,
              description: `${selectedCategory?.title} - ${item.name}`
            })),
            ...config.extraItems.map(item => ({
              name: item.name,
              quantity: item.quantity,
              unit_price: item.priceExVat,
              total_price: item.priceExVat * item.quantity,
              description: `Extra - ${item.name}`
            }))
          ];

          // Add videodeurbel if included
          if (config.videoDoorbell.included) {
            allItems.push({
              name: videoDoorbell.name,
              quantity: 1,
              unit_price: config.videoDoorbell.free ? 0 : videoDoorbell.priceExVat,
              total_price: config.videoDoorbell.free ? 0 : videoDoorbell.priceExVat,
              description: config.videoDoorbell.free ? 'VIDEODEURBEL (GRATIS)' : 'VIDEODEURBEL'
            });
          }

          const quotationData = {
            id: Date.now(), // Temporary ID
            quotation_number: `QS-${Date.now()}`,
            title: `Snelle Verkoop - ${selectedCategory?.title}`,
            customer_id: config.customerId,
            customer_name: selectedCustomer.name,
            customer_address: selectedCustomer.address,
            customer_city: selectedCustomer.city,
            customer_postal_code: selectedCustomer.postal_code,
            customer_phone: selectedCustomer.phone,
            customer_email: selectedCustomer.email,
            introduction: `Offerte voor ${selectedCategory?.title} systeem - Particuliere installatie`,
            conclusion: `Maandelijkse kosten: €${pricing.monthlyCost.toFixed(2)} per maand`,
            valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
            discount_percentage: config.baseDiscount,
            subtotal: pricing.installationCost / 1.21, // Convert back to ex VAT
            discount_amount: (pricing.installationCost / 1.21) * (config.baseDiscount / 100),
            total_excl_vat: pricing.installationCost / 1.21,
            vat_amount: pricing.installationCost - (pricing.installationCost / 1.21),
            total_incl_vat: pricing.installationCost,
            monthly_cost: pricing.monthlyCost,
            items: allItems,
            created_by: user?.id,
            created_at: new Date().toISOString()
          };

          const result = await generateQuotationDocument(quotationData, selectedTemplate.id);

          showConfirmation({
            title: 'Document succesvol gegenereerd',
            message: `Het document "${result.filename || 'Snelle Verkoop Offerte'}" is succesvol gegenereerd en opgeslagen bij de klant documenten.`,
            confirmText: 'Ga naar Offertes',
            cancelText: 'Nieuwe Offerte',
            onConfirm: () => {
              navigate('/quotations');
            },
            onCancel: () => {
              // Reset form for new quotation
              setCurrentStep('category');
              setSelectedCategory(null);
              setConfig({
                customerId: '',
                baseDiscount: 0,
                videoDoorbell: { included: false, free: false },
                extraItems: []
              });
            }
          });

        } catch (error: any) {
          console.error('Failed to generate document:', error);
          alert(error.response?.data?.error || 'Fout bij genereren van document');
        } finally {
          setGenerating(false);
        }
      }
    });
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <button
            onClick={handleBack}
            className="mr-4 p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-full"
          >
            <FaArrowLeft />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Snelle Verkoop</h1>
            <p className="text-gray-600">
              {currentStep === 'category' 
                ? 'Kies het type systeem voor deze particuliere klant'
                : `Configureer ${selectedCategory?.title} systeem`
              }
            </p>
          </div>
        </div>

        {/* Progress indicator */}
        <div className="flex items-center space-x-4">
          <div className={`flex items-center ${currentStep === 'category' ? 'text-blue-600' : 'text-green-600'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
              currentStep === 'category' ? 'bg-blue-600' : 'bg-green-600'
            }`}>
              1
            </div>
            <span className="ml-2 font-medium">Categorie</span>
          </div>
          <div className="flex-1 h-1 bg-gray-200 rounded">
            <div className={`h-1 rounded transition-all duration-300 ${
              currentStep === 'configuration' ? 'w-full bg-green-600' : 'w-0 bg-blue-600'
            }`}></div>
          </div>
          <div className={`flex items-center ${currentStep === 'configuration' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
              currentStep === 'configuration' ? 'bg-blue-600' : 'bg-gray-400'
            }`}>
              2
            </div>
            <span className="ml-2 font-medium">Configuratie</span>
          </div>
        </div>
      </div>

      {/* Category Selection */}
      {currentStep === 'category' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {categories.map((category) => (
            <div
              key={category.id}
              onClick={() => handleCategorySelect(category)}
              className={`
                border-2 rounded-lg p-8 cursor-pointer transition-all duration-200
                ${category.color}
                hover:shadow-xl transform hover:-translate-y-2
              `}
            >
              <div className="text-center">
                <div className="mb-6 flex justify-center">
                  {category.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {category.title}
                </h3>
                <p className="text-gray-600">
                  {category.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Configuration Step */}
      {currentStep === 'configuration' && selectedCategory && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Configuration Panel */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Selection */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Klant Selectie</h3>
              <select
                value={config.customerId}
                onChange={(e) => setConfig({ ...config, customerId: e.target.value })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Selecteer een klant...</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} - {customer.email}
                  </option>
                ))}
              </select>
            </div>

            {/* Base System */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basis Systeem (Altijd inbegrepen)</h3>
              <div className="space-y-3">
                {baseSystemItems.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                    <div>
                      <span className="font-medium">{item.quantity}x {item.name}</span>
                    </div>
                    <div className="text-gray-600">
                      €{(item.priceExVat * item.quantity).toFixed(2)} ex BTW
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Discount Settings */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Korting (Max 25%)</h3>
              <div className="flex items-center space-x-4">
                <input
                  type="range"
                  min="0"
                  max="25"
                  value={config.baseDiscount}
                  onChange={(e) => setConfig({ ...config, baseDiscount: parseInt(e.target.value) })}
                  className="flex-1"
                />
                <div className="text-lg font-semibold text-blue-600">
                  {config.baseDiscount}%
                </div>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                Originele prijs: €999,99 → Met korting: €{(999.99 * (1 - config.baseDiscount / 100)).toFixed(2)}
              </div>
            </div>

            {/* Videodeurbel */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Videodeurbel</h3>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.videoDoorbell.included}
                    onChange={(e) => setConfig({
                      ...config,
                      videoDoorbell: { ...config.videoDoorbell, included: e.target.checked }
                    })}
                    className="mr-3"
                  />
                  <span>Videodeurbel toevoegen (€249,99)</span>
                </label>
                {config.videoDoorbell.included && (
                  <label className="flex items-center ml-6">
                    <input
                      type="checkbox"
                      checked={config.videoDoorbell.free}
                      onChange={(e) => setConfig({
                        ...config,
                        videoDoorbell: { ...config.videoDoorbell, free: e.target.checked }
                      })}
                      className="mr-3"
                    />
                    <span className="text-green-600 font-medium">Gratis maken</span>
                  </label>
                )}
              </div>
            </div>

            {/* Extra Items */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Extra Artikelen</h3>
              <div className="space-y-3">
                {availableExtraItems.map((item, index) => {
                  const quantity = getExtraItemQuantity(item.name);
                  return (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                      <div>
                        <span className="font-medium">{item.name}</span>
                        <div className="text-sm text-gray-600">€{item.priceExVat.toFixed(2)} ex BTW</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleRemoveExtraItem(item.name)}
                          disabled={quantity === 0}
                          className="p-1 text-red-600 hover:bg-red-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <FaMinus />
                        </button>
                        <span className="w-8 text-center font-medium">{quantity}</span>
                        <button
                          onClick={() => handleAddExtraItem(item)}
                          className="p-1 text-green-600 hover:bg-green-50 rounded"
                        >
                          <FaPlus />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Pricing Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Prijsoverzicht</h3>

              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>Basis systeem (ex BTW):</span>
                  <span>€{pricing.baseSystemTotalExVat.toFixed(2)}</span>
                </div>

                {pricing.extraItemsTotalExVat > 0 && (
                  <div className="flex justify-between">
                    <span>Extra artikelen (ex BTW):</span>
                    <span>€{pricing.extraItemsTotalExVat.toFixed(2)}</span>
                  </div>
                )}

                {config.videoDoorbell.included && (
                  <div className="flex justify-between">
                    <span>Videodeurbel:</span>
                    <span className={config.videoDoorbell.free ? 'text-green-600 font-medium' : ''}>
                      {config.videoDoorbell.free ? 'GRATIS' : `€${videoDoorbell.priceInclVat.toFixed(2)}`}
                    </span>
                  </div>
                )}

                <hr className="my-3" />

                <div className="flex justify-between font-semibold text-lg">
                  <span>Installatiekosten:</span>
                  <span className="text-blue-600">€{pricing.installationCost.toFixed(2)}</span>
                </div>

                <div className="flex justify-between font-semibold text-lg">
                  <span>Maandelijkse kosten:</span>
                  <span className="text-green-600">€{pricing.monthlyCost.toFixed(2)}</span>
                </div>
              </div>

              <div className="mt-6 space-y-3">
                {/* Template Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Template
                  </label>
                  <select
                    value={selectedTemplate?.id || ''}
                    onChange={(e) => {
                      const template = templates.find(t => t.id === parseInt(e.target.value));
                      setSelectedTemplate(template || null);
                    }}
                    className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="">Selecteer template...</option>
                    {templates.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                </div>

                <button
                  onClick={handleGenerateDocument}
                  disabled={!config.customerId || !selectedTemplate || generating}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {generating ? (
                    <>
                      <LoadingSpinner size="sm" fullScreen={false} message="" className="mr-2" /> Genereren...
                    </>
                  ) : (
                    <>
                      <FaFileAlt className="mr-2" /> Offerte Genereren
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuickSaleForm;
