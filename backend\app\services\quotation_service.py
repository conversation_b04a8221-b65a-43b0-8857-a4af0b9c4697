"""
Quotation service module.
This module provides business logic for quotations.
"""
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta, timezone
from marshmallow import ValidationError
from app.repositories.quotation_repository import QuotationRepository, QuotationItemRepository
from app.repositories.customer_repository import CustomerRepository
from app.repositories.product_repository import ProductRepository
from app.repositories.document_repository import DocumentRepository
from app.schemas.quotation_schema import quotation_schema, quotations_schema
from app import db

# Configure logging
logger = logging.getLogger(__name__)

class QuotationService:
    """Service for quotation-related operations."""

    def __init__(self):
        """Initialize the service with repositories."""
        self.quotation_repo = QuotationRepository()
        self.item_repo = QuotationItemRepository()
        self.customer_repo = CustomerRepository()
        self.product_repo = ProductRepository()
        self.document_repo = DocumentRepository()

    def get_all_quotations(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Get all quotations with pagination.

        Args:
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations, total = self.quotation_repo.get_all(page, per_page)
        return [quotation.to_dict() for quotation in quotations], total

    def get_quotation_by_id(self, quotation_id: int) -> Dict:
        """
        Get a quotation by ID.

        Args:
            quotation_id: Quotation ID

        Returns:
            Quotation data

        Raises:
            Exception: If quotation not found
        """
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")
        return quotation.to_dict()

    def get_quotations_by_customer(self, customer_id: int, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Get quotations for a customer with pagination.

        Args:
            customer_id: Customer ID
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations, total = self.quotation_repo.get_by_customer(customer_id, page, per_page)
        return [quotation.to_dict() for quotation in quotations], total

    def search_quotations(self, search_term: str, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Search quotations by title, quotation number, or customer name.

        Args:
            search_term: Search term
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations, total = self.quotation_repo.search(search_term, page, per_page)
        return [quotation.to_dict() for quotation in quotations], total

    def create_quotation(self, quotation_data: Dict) -> Dict:
        """
        Create a new quotation.

        Args:
            quotation_data: Quotation data

        Returns:
            Created quotation data
        """
        # Validate with schema
        errors = quotation_schema.validate(quotation_data)
        if errors:
            raise Exception(f"Validation failed: {errors}")

        # Check if customer exists
        customer = self.customer_repo.get_by_id(quotation_data["customer_id"])
        if not customer:
            raise Exception("Customer not found")

        # Generate quotation number if not provided
        if not quotation_data.get("quotation_number"):
            quotation_data["quotation_number"] = self.quotation_repo.get_next_quotation_number()

        # Set default valid until date (30 days from now) if not provided
        if not quotation_data.get("valid_until"):
            quotation_data["valid_until"] = datetime.now(timezone.utc) + timedelta(days=30)

        # Set default status if not provided
        if not quotation_data.get("status"):
            quotation_data["status"] = "concept"

        # Create quotation
        quotation = self.quotation_repo.create(quotation_data)

        # Create a document for the quotation using DOCX template
        try:
            logger.info(f"Creating document for new quotation {quotation.id}")
            document = self.generate_quotation_document(quotation.id)

            # Link the document to the quotation
            quotation.document_id = document["id"]
            logger.info(f"Linked document {document['id']} to quotation {quotation.id}")
            db.session.commit()
        except Exception as e:
            logger.error(f"Failed to create document for quotation {quotation.id}: {str(e)}")
            # Continue even if document creation fails

        return quotation.to_dict()

    def update_quotation(self, quotation_id: int, quotation_data: Dict) -> Dict:
        """
        Update a quotation.

        Args:
            quotation_id: Quotation ID
            quotation_data: Updated quotation data

        Returns:
            Updated quotation data

        Raises:
            Exception: If quotation not found or validation fails
        """
        # Get quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Create a copy of the data to avoid modifying the original
        data_to_validate = quotation_data.copy()

        # Filter out read-only fields that should not be validated
        read_only_fields = [
            'created_at', 'updated_at', 'customer_name', 'created_by_name',
            'subtotal', 'discount_amount', 'total_excl_vat', 'vat_amount',
            'total_incl_vat', 'items'
        ]

        for field in read_only_fields:
            if field in data_to_validate:
                data_to_validate.pop(field)

        # Log the data we're going to validate
        logger.info(f"Validating data for quotation {quotation_id}: {data_to_validate}")

        # Add required fields from the existing quotation if they're not in the update data
        if "customer_id" not in data_to_validate:
            data_to_validate["customer_id"] = quotation.customer_id

        if "created_by" not in data_to_validate:
            data_to_validate["created_by"] = quotation.created_by

        if "title" not in data_to_validate:
            data_to_validate["title"] = quotation.title

        # Validate with schema
        errors = quotation_schema.validate(data_to_validate)
        if errors:
            logger.error(f"Validation failed for quotation {quotation_id}: {errors}")
            raise ValidationError(errors)

        # Check if customer exists if customer_id is provided
        if "customer_id" in quotation_data:
            customer = self.customer_repo.get_by_id(quotation_data["customer_id"])
            if not customer:
                raise Exception("Customer not found")

        try:
            # Update quotation
            updated_quotation = self.quotation_repo.update(quotation, quotation_data)
            return updated_quotation.to_dict()
        except Exception as e:
            logger.error(f"Error updating quotation {quotation_id}: {str(e)}")
            raise Exception(f"Failed to update quotation: {str(e)}")

    def delete_quotation(self, quotation_id: int) -> bool:
        """
        Delete a quotation.

        Args:
            quotation_id: Quotation ID

        Returns:
            True if deleted, False if not found
        """
        return self.quotation_repo.delete(quotation_id)

    def add_item_to_quotation(self, quotation_id: int, item_data: Dict) -> Dict:
        """
        Add an item to a quotation.

        Args:
            quotation_id: Quotation ID
            item_data: Item data

        Returns:
            Added item data

        Raises:
            Exception: If quotation not found or validation fails
        """
        # Get quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Set quotation ID in item data
        item_data["quotation_id"] = quotation_id

        # If product_id is provided, get product details
        if item_data.get("product_id"):
            product = self.product_repo.get_by_id(item_data["product_id"])
            if not product:
                raise Exception("Product not found")

            # Set product details if not provided
            if not item_data.get("description"):
                item_data["description"] = product.name

            if not item_data.get("unit_price"):
                item_data["unit_price"] = product.gross_price

        # Create item
        item = self.item_repo.create(item_data)
        return item.to_dict()

    def update_item(self, item_id: int, item_data: Dict) -> Dict:
        """
        Update a quotation item.

        Args:
            item_id: Item ID
            item_data: Updated item data

        Returns:
            Updated item data

        Raises:
            Exception: If item not found or validation fails
        """
        # Get item
        item = self.item_repo.get_by_id(item_id)
        if not item:
            raise Exception("Quotation item not found")

        # Update item
        updated_item = self.item_repo.update(item, item_data)
        return updated_item.to_dict()

    def delete_item(self, item_id: int) -> bool:
        """
        Delete a quotation item.

        Args:
            item_id: Item ID

        Returns:
            True if deleted, False if not found
        """
        return self.item_repo.delete(item_id)

    def generate_quotation_document(self, quotation_id: int) -> Dict:
        """
        Generate a DOCX document for a quotation using templates.

        Args:
            quotation_id: Quotation ID

        Returns:
            Document data

        Raises:
            Exception: If quotation not found or document generation fails
        """
        # Get quotation with all related data
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Get customer data
        customer = self.customer_repo.get_by_id(quotation.customer_id)
        if not customer:
            raise Exception("Customer not found")

        # Get quotation items
        items = self.item_repo.get_by_quotation_id(quotation_id)

        # Prepare quotation data for template
        quotation_data = quotation.to_dict()
        quotation_data['customer_name'] = customer.name
        quotation_data['customer_address'] = customer.address
        quotation_data['customer_city'] = customer.city
        quotation_data['customer_postal_code'] = customer.postal_code
        quotation_data['customer_phone'] = customer.phone
        quotation_data['customer_email'] = customer.email
        quotation_data['items'] = [item.to_dict() for item in items]

        # Use quotation template service to generate document
        from app.services.quotation_template_service import QuotationTemplateService
        template_service = QuotationTemplateService()

        try:
            document = template_service.generate_quotation_document(quotation_data)
            logger.info(f"Generated quotation document for quotation {quotation_id}")
            return document
        except Exception as e:
            logger.error(f"Failed to generate quotation document: {str(e)}")
            raise Exception(f"Failed to generate quotation document: {str(e)}")



    def update_quotation_status(self, quotation_id: int, status: str) -> Dict:
        """
        Update only the status of a quotation.

        Args:
            quotation_id: Quotation ID
            status: New status

        Returns:
            Updated quotation data

        Raises:
            Exception: If quotation not found or status is invalid
        """
        # Get quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            logger.error(f"Quotation not found with ID: {quotation_id}")
            raise Exception("Quotation not found")

        # Validate status
        valid_statuses = ["concept", "sent", "accepted", "rejected"]
        if status not in valid_statuses:
            logger.error(f"Invalid status: {status}. Must be one of: {', '.join(valid_statuses)}")
            raise Exception(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")

        # Check if status is changing
        if quotation.status == status:
            logger.info(f"Quotation {quotation_id} status already set to {status}")
            return quotation.to_dict()

        # Log status change
        logger.info(f"Updating quotation {quotation_id} status from {quotation.status} to {status}")

        # Update status
        quotation.status = status

        # If status is accepted or rejected, update the timestamp
        if status in ["accepted", "rejected"]:
            quotation.updated_at = datetime.now(timezone.utc)

        # If the quotation doesn't have a document yet, create one
        if not quotation.document_id:
            try:
                # Create a document for the quotation using DOCX template
                logger.info(f"Creating document for quotation {quotation_id}")
                document = self.generate_quotation_document(quotation_id)

                # Link the document to the quotation
                quotation.document_id = document["id"]
                logger.info(f"Linked document {document['id']} to quotation {quotation_id}")
            except Exception as e:
                logger.error(f"Failed to create document for quotation {quotation_id}: {str(e)}")
                # Continue with the status update even if document creation fails

        db.session.commit()

        return quotation.to_dict()



    def process_single_rejected_quotation(self, quotation_id: int) -> Dict:
        """
        Process a single rejected quotation by deleting it and its associated customer
        if the customer has no other quotations.

        Args:
            quotation_id: Quotation ID

        Returns:
            Dictionary with information about the operation
        """
        # Get the quotation
        quotation = self.quotation_repo.get_by_id(quotation_id)
        if not quotation:
            raise Exception("Quotation not found")

        # Check if the quotation is rejected
        if quotation.status != "rejected":
            # If not rejected, reject it first
            quotation.status = "rejected"
            db.session.commit()

        customer_id = quotation.customer_id
        customer_deleted = False

        # Check if customer has other non-rejected quotations
        other_quotations = self.quotation_repo.get_by_customer_and_not_status(
            customer_id, "rejected"
        )

        # Delete the quotation
        self.quotation_repo.delete(quotation_id)

        # If customer has no other quotations, delete the customer
        if not other_quotations:
            self.customer_repo.delete(customer_id)
            customer_deleted = True

        return {
            "quotation_deleted": True,
            "customer_deleted": customer_deleted
        }
